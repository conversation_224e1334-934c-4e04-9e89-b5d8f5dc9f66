const { spawn } = require('child_process');
const http = require('http');

console.log('🚀 启动 Electron 开发环境...\n');

// 检查端口是否可用
function checkServer(url, maxAttempts = 30) {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    
    function check() {
      attempts++;
      console.log(`⏳ 检查服务器状态 (${attempts}/${maxAttempts})...`);
      
      const request = http.get(url, (res) => {
        if (res.statusCode === 200) {
          console.log('✅ Web 服务器已就绪！');
          resolve();
        } else {
          if (attempts < maxAttempts) {
            setTimeout(check, 2000);
          } else {
            reject(new Error('服务器启动超时'));
          }
        }
      });
      
      request.on('error', () => {
        if (attempts < maxAttempts) {
          setTimeout(check, 2000);
        } else {
          reject(new Error('无法连接到服务器'));
        }
      });
      
      request.setTimeout(5000, () => {
        request.destroy();
        if (attempts < maxAttempts) {
          setTimeout(check, 2000);
        } else {
          reject(new Error('连接超时'));
        }
      });
    }
    
    check();
  });
}

async function startDev() {
  try {
    // 启动 Web 开发服务器
    console.log('📦 启动 Web 开发服务器...');
    const webServer = spawn('npm', ['run', 'start:dev'], {
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true,
      cwd: process.cwd()
    });
    
    // 监听 Web 服务器输出
    webServer.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('[Web]', output.trim());
    });
    
    webServer.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('[Web Error]', output.trim());
    });
    
    // 等待服务器启动
    await checkServer('http://localhost:8002');
    
    // 启动 Electron
    console.log('🖥️  启动 Electron...');
    const electron = spawn('electron', ['.'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd(),
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    // 处理进程退出
    electron.on('close', (code) => {
      console.log(`\n🔴 Electron 已退出 (代码: ${code})`);
      console.log('🛑 停止 Web 服务器...');
      webServer.kill('SIGTERM');
      process.exit(0);
    });
    
    webServer.on('close', (code) => {
      console.log(`\n🔴 Web 服务器已退出 (代码: ${code})`);
      console.log('🛑 停止 Electron...');
      electron.kill('SIGTERM');
      process.exit(0);
    });
    
    // 处理错误
    electron.on('error', (err) => {
      console.error('❌ Electron 启动失败:', err.message);
      webServer.kill('SIGTERM');
      process.exit(1);
    });
    
    webServer.on('error', (err) => {
      console.error('❌ Web 服务器启动失败:', err.message);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭开发环境...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭开发环境...');
  process.exit(0);
});

startDev();
