# Prompt2Video - 跨平台桌面应用

这是一个基于 Ant Design Pro 构建的跨平台应用，既可以在浏览器中运行，也可以作为 Electron 桌面应用使用。

## 技术栈

- **前端框架**: React + Ant Design Pro
- **构建工具**: Umi Max
- **桌面应用**: Electron
- **开发语言**: TypeScript

## 环境准备

安装依赖：

```bash
npm install
```

或

```bash
yarn
```

## 可用脚本

项目提供了多种运行模式的脚本：

### 🌐 Web 模式（浏览器）

启动开发服务器：
```bash
npm start
# 或
npm run start:dev
```

构建生产版本：
```bash
npm run build
```

预览生产版本：
```bash
npm run preview
```

### 🖥️ Electron 模式（桌面应用）

开发模式（同时启动Web服务器和Electron）：
```bash
npm run electron:dev
```

仅启动Electron（需要先启动Web服务器）：
```bash
npm run electron
```

构建桌面应用：
```bash
npm run build:electron
```

打包桌面应用（仅打包，不安装）：
```bash
npm run pack
```

### 🔧 开发工具

代码检查：
```bash
npm run lint
```

运行测试：
```bash
npm test
```

类型检查：
```bash
npm run tsc
```

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Prompt2Video
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发环境**

   Web模式：
   ```bash
   npm run start:dev
   ```

   Electron模式：
   ```bash
   npm run electron:dev
   ```

## 📁 项目结构

```
Prompt2Video/
├── public/                 # 静态资源
│   ├── main.js            # Electron 主进程
│   ├── preload.js         # Electron 预加载脚本
│   └── icons/             # 应用图标
├── src/                   # 源代码
│   ├── components/        # 公共组件
│   ├── pages/            # 页面组件
│   ├── services/         # API 服务
│   ├── utils/            # 工具函数
│   └── app.tsx           # 应用入口
├── config/               # 配置文件
├── scripts/              # 构建脚本
└── package.json          # 项目配置
```

## 🔧 环境检测

应用会自动检测运行环境：
- **Electron 环境**: 显示为"Electron 桌面应用"
- **浏览器环境**: 显示为"Web 浏览器"

## 📦 打包说明

- **Web版本**: 构建后的文件在 `dist/` 目录
- **桌面应用**: 打包后的文件在 `electron-dist/` 目录

支持的平台：
- Windows (NSIS 安装包)
- macOS (DMG 安装包)
- Linux (AppImage)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
