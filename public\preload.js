const { contextBridge, ipcRenderer } = require('electron');

// 暴露受保护的方法，允许渲染进程使用
// ipcRenderer，而不会暴露整个对象
contextBridge.exposeInMainWorld('electronAPI', {
  // 检查是否在Electron环境中运行
  isElectron: true,
  
  // 获取平台信息
  platform: process.platform,
  
  // 版本信息
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron,
  },
  
  // 可以添加更多需要的API
  // 例如：文件操作、系统通知等
  
  // 示例：发送消息到主进程
  sendMessage: (message) => {
    ipcRenderer.invoke('message-from-renderer', message);
  },
  
  // 示例：监听主进程消息
  onMessage: (callback) => {
    ipcRenderer.on('message-from-main', callback);
  },
  
  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// 在窗口加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
  // 可以在这里添加一些初始化代码
  console.log('Preload script loaded');
});
