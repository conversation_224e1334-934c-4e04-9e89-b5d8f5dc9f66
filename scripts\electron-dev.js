const { spawn } = require('child_process');
const { createServer } = require('http');
const path = require('path');

// 检查端口是否可用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = createServer();
    server.listen(port, () => {
      server.close(() => resolve(true));
    });
    server.on('error', () => resolve(false));
  });
}

// 等待服务器启动
function waitForServer(url, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    function check() {
      const http = require('http');
      const request = http.get(url, (res) => {
        if (res.statusCode === 200) {
          resolve();
        } else {
          setTimeout(check, 1000);
        }
      });
      
      request.on('error', () => {
        if (Date.now() - startTime > timeout) {
          reject(new Error('Timeout waiting for server'));
        } else {
          setTimeout(check, 1000);
        }
      });
    }
    
    check();
  });
}

async function startElectronDev() {
  console.log('🚀 Starting Electron development environment...');
  
  // 启动Web开发服务器
  console.log('📦 Starting web development server...');
  const webServer = spawn('npm', ['run', 'start:dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  });
  
  try {
    // 等待Web服务器启动
    console.log('⏳ Waiting for web server to start...');
    await waitForServer('http://localhost:8000');
    console.log('✅ Web server is ready!');
    
    // 启动Electron
    console.log('🖥️  Starting Electron...');
    const electron = spawn('electron', ['.'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd(),
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    // 处理进程退出
    electron.on('close', () => {
      console.log('🔴 Electron closed, stopping web server...');
      webServer.kill();
      process.exit(0);
    });
    
    webServer.on('close', () => {
      console.log('🔴 Web server closed, stopping Electron...');
      electron.kill();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Error starting development environment:', error.message);
    webServer.kill();
    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development environment...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development environment...');
  process.exit(0);
});

startElectronDev();
