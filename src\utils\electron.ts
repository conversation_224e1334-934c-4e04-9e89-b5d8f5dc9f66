/**
 * Electron 环境检测和工具函数
 */

// 检查是否在Electron环境中运行
export const isElectron = (): boolean => {
  // 方法1: 检查window.electronAPI（由preload脚本注入）
  if (typeof window !== 'undefined' && (window as any).electronAPI) {
    return true;
  }
  
  // 方法2: 检查user agent
  if (typeof navigator !== 'undefined') {
    return navigator.userAgent.toLowerCase().includes('electron');
  }
  
  // 方法3: 检查process（在某些情况下可能可用）
  if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
    return true;
  }
  
  return false;
};

// 获取Electron API（如果可用）
export const getElectronAPI = () => {
  if (typeof window !== 'undefined' && (window as any).electronAPI) {
    return (window as any).electronAPI;
  }
  return null;
};

// 获取平台信息
export const getPlatform = (): string => {
  const electronAPI = getElectronAPI();
  if (electronAPI && electronAPI.platform) {
    return electronAPI.platform;
  }
  
  // 回退到浏览器检测
  if (typeof navigator !== 'undefined') {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('mac')) return 'darwin';
    if (userAgent.includes('win')) return 'win32';
    if (userAgent.includes('linux')) return 'linux';
  }
  
  return 'unknown';
};

// 检查是否为开发环境
export const isDev = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

// 获取版本信息
export const getVersions = () => {
  const electronAPI = getElectronAPI();
  if (electronAPI && electronAPI.versions) {
    return electronAPI.versions;
  }
  return null;
};

// 发送消息到主进程（仅在Electron环境中可用）
export const sendToMain = (message: any) => {
  const electronAPI = getElectronAPI();
  if (electronAPI && electronAPI.sendMessage) {
    electronAPI.sendMessage(message);
  } else {
    console.warn('sendToMain: Not running in Electron environment');
  }
};

// 监听主进程消息（仅在Electron环境中可用）
export const onMainMessage = (callback: (event: any, ...args: any[]) => void) => {
  const electronAPI = getElectronAPI();
  if (electronAPI && electronAPI.onMessage) {
    electronAPI.onMessage(callback);
  } else {
    console.warn('onMainMessage: Not running in Electron environment');
  }
};
