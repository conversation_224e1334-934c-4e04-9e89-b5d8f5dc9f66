const fs = require('fs');
const path = require('path');

console.log('🔧 修复 Electron 静态资源路径...');

const distPath = path.join(process.cwd(), 'dist');
const indexPath = path.join(distPath, 'index.html');

if (!fs.existsSync(indexPath)) {
  console.log('❌ 找不到 dist/index.html 文件');
  process.exit(1);
}

try {
  // 读取 HTML 文件
  let htmlContent = fs.readFileSync(indexPath, 'utf8');
  
  // 修复 CSS 和 JS 文件路径
  htmlContent = htmlContent.replace(/href="\/([^"]+\.css)"/g, 'href="./$1"');
  htmlContent = htmlContent.replace(/src="\/([^"]+\.js)"/g, 'src="./$1"');
  htmlContent = htmlContent.replace(/src="\\([^"]+\.js)"/g, 'src="./$1"');

  // 添加 CSP 安全策略（仅在开发模式下）
  if (!htmlContent.includes('Content-Security-Policy')) {
    htmlContent = htmlContent.replace(
      '<meta http-equiv="X-UA-Compatible" content="ie=edge">',
      '<meta http-equiv="X-UA-Compatible" content="ie=edge">\n<meta http-equiv="Content-Security-Policy" content="default-src \'self\' \'unsafe-inline\' \'unsafe-eval\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\';">'
    );
  }
  
  // 写回文件
  fs.writeFileSync(indexPath, htmlContent, 'utf8');
  
  console.log('✅ 路径修复完成！');
  
  // 显示修复后的内容
  console.log('\n📄 修复后的 HTML 文件:');
  console.log(htmlContent);
  
} catch (error) {
  console.error('❌ 修复失败:', error.message);
  process.exit(1);
}
