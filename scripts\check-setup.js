const fs = require('fs');
const path = require('path');

console.log('🔍 检查 Electron 配置...\n');

// 检查必要文件
const requiredFiles = [
  'public/main.js',
  'public/preload.js',
  'src/utils/electron.ts',
  'package.json'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
    allFilesExist = false;
  }
});

// 检查 package.json 配置
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  console.log('\n📦 检查 package.json 配置:');
  
  // 检查 main 字段
  if (packageJson.main === 'public/main.js') {
    console.log('✅ main 字段配置正确');
  } else {
    console.log('❌ main 字段配置错误');
    allFilesExist = false;
  }
  
  // 检查 homepage 字段
  if (packageJson.homepage === './') {
    console.log('✅ homepage 字段配置正确');
  } else {
    console.log('❌ homepage 字段配置错误');
    allFilesExist = false;
  }
  
  // 检查必要的依赖
  const requiredDeps = ['electron', 'electron-builder', 'concurrently', 'wait-on'];
  const devDeps = packageJson.devDependencies || {};
  
  console.log('\n🔧 检查依赖:');
  requiredDeps.forEach(dep => {
    if (devDeps[dep]) {
      console.log(`✅ ${dep} - 已安装`);
    } else {
      console.log(`❌ ${dep} - 未安装`);
      allFilesExist = false;
    }
  });
  
  // 检查脚本
  const requiredScripts = ['electron', 'electron:dev', 'build:electron'];
  const scripts = packageJson.scripts || {};
  
  console.log('\n📜 检查脚本:');
  requiredScripts.forEach(script => {
    if (scripts[script]) {
      console.log(`✅ ${script} - 已配置`);
    } else {
      console.log(`❌ ${script} - 未配置`);
      allFilesExist = false;
    }
  });
  
} catch (error) {
  console.log('❌ 无法读取 package.json');
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 所有配置检查通过！');
  console.log('\n📋 下一步操作:');
  console.log('1. 安装依赖: npm install');
  console.log('2. 启动Web模式: npm run start:dev');
  console.log('3. 启动Electron模式: npm run electron:dev');
  console.log('4. 构建桌面应用: npm run build:electron');
} else {
  console.log('❌ 配置检查失败，请检查上述错误！');
  process.exit(1);
}

console.log('\n💡 提示: 确保已安装所有依赖包');
console.log('   运行: npm install');
