# Electron 开发环境启动指南

## 🚀 快速启动（推荐方法）

### 方法一：分步启动（最稳定）

1. **第一步：启动 Web 开发服务器**
   ```bash
   npm run start:dev
   ```
   等待看到类似这样的输出：
   ```
   App listening at:
   - Local:   http://localhost:8000
   - Network: http://************:8000
   ```

2. **第二步：在新的终端窗口启动 Electron**
   ```bash
   npm run electron:dev:simple
   ```

### 方法二：使用批处理文件（Windows）

双击运行项目根目录下的 `start-electron.bat` 文件

### 方法三：自动启动（如果可用）

```bash
npm run electron:dev
```

## 🔧 故障排除

### 问题1：concurrently 命令不存在
**解决方案：**
```bash
npm install
```

### 问题2：Electron 无法加载页面
**检查：**
1. Web 服务器是否正在运行在 http://localhost:8000
2. 浏览器中能否正常访问 http://localhost:8000

### 问题3：端口被占用
**解决方案：**
1. 关闭其他占用 8000 端口的程序
2. 或者修改 `config/config.ts` 中的端口配置

### 问题4：Electron 窗口空白
**检查：**
1. 打开 Electron 开发者工具（F12）查看控制台错误
2. 确认 Web 服务器正常运行

## 📋 验证安装

运行配置检查脚本：
```bash
node scripts/check-setup.js
```

## 🎯 开发流程

1. **开发 Web 功能**：在浏览器中访问 http://localhost:8000
2. **测试桌面功能**：使用 Electron 应用
3. **构建生产版本**：`npm run build:electron`

## 📦 打包发布

```bash
# 构建并打包桌面应用
npm run build:electron

# 仅打包（不创建安装程序）
npm run pack
```

## 🔍 环境检测

应用会自动检测运行环境：
- 在浏览器中显示："Web 浏览器"
- 在 Electron 中显示："Electron 桌面应用"

## 💡 提示

- 确保 Node.js 版本 >= 20.0.0
- 如果遇到问题，尝试删除 `node_modules` 文件夹并重新安装依赖
- Windows 用户可能需要以管理员身份运行某些命令
