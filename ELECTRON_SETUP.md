# Electron 开发环境启动指南

## 🚀 快速启动（推荐方法）

### 方法一：独立 Electron 开发模式（推荐）

```bash
npm run electron:dev
```

**特点：**
- ✅ 不依赖外部 Web 服务器
- ✅ 自动构建应用（首次运行）
- ✅ 自动打开开发者工具（F12）
- ✅ 一键启动，简单快捷

### 方法二：与 Web 服务器联动开发

```bash
npm run electron:dev:with-web
```

**特点：**
- 🔄 热重载支持
- 🌐 同时支持浏览器和 Electron 开发
- 📡 依赖 Web 开发服务器

### 方法三：手动分步启动

1. **构建应用**：
   ```bash
   npm run build:dev
   ```

2. **启动 Electron**：
   ```bash
   npm run electron:dev:manual
   ```

## 🔧 故障排除

### 问题1：concurrently 命令不存在
**解决方案：**
```bash
npm install
```

### 问题2：Electron 无法加载页面
**检查：**
1. Web 服务器是否正在运行在 http://localhost:8000
2. 浏览器中能否正常访问 http://localhost:8000

### 问题3：端口被占用
**解决方案：**
1. 关闭其他占用 8000 端口的程序
2. 或者修改 `config/config.ts` 中的端口配置

### 问题4：Electron 窗口空白
**检查：**
1. 打开 Electron 开发者工具（F12）查看控制台错误
2. 确认 Web 服务器正常运行

## 📋 验证安装

运行配置检查脚本：
```bash
node scripts/check-setup.js
```

## 🎯 开发流程

1. **开发 Web 功能**：在浏览器中访问 http://localhost:8000
2. **测试桌面功能**：使用 Electron 应用
3. **构建生产版本**：`npm run build:electron`

## 📦 打包发布

```bash
# 构建并打包桌面应用
npm run build:electron

# 仅打包（不创建安装程序）
npm run pack
```

## 🔍 环境检测

应用会自动检测运行环境：
- 在浏览器中显示："Web 浏览器"
- 在 Electron 中显示："Electron 桌面应用"

## 💡 提示

- 确保 Node.js 版本 >= 20.0.0
- 如果遇到问题，尝试删除 `node_modules` 文件夹并重新安装依赖
- Windows 用户可能需要以管理员身份运行某些命令
