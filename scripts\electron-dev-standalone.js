const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动独立 Electron 开发环境...\n');

// 检查 dist 目录是否存在
const distPath = path.join(process.cwd(), 'dist');
const indexPath = path.join(distPath, 'index.html');

async function buildAndStart() {
  try {
    // 检查是否需要构建
    if (!fs.existsSync(indexPath)) {
      console.log('📦 首次运行，正在构建应用...');
      await runBuild();
    } else {
      console.log('✅ 发现已构建的应用，直接启动 Electron');
      console.log('💡 如需重新构建，请运行: npm run build:dev\n');
    }
    
    // 启动 Electron
    console.log('🖥️  启动 Electron 开发模式...');
    const electron = spawn('electron', ['.'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd(),
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });
    
    electron.on('close', (code) => {
      console.log(`\n🔴 Electron 已退出 (代码: ${code})`);
      process.exit(0);
    });
    
    electron.on('error', (err) => {
      console.error('❌ Electron 启动失败:', err.message);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

function runBuild() {
  return new Promise((resolve, reject) => {
    const build = spawn('npm', ['run', 'build:dev'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });
    
    build.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 构建完成！\n');
        resolve();
      } else {
        reject(new Error(`构建失败，退出代码: ${code}`));
      }
    });
    
    build.on('error', (err) => {
      reject(new Error(`构建失败: ${err.message}`));
    });
  });
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭 Electron...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭 Electron...');
  process.exit(0);
});

buildAndStart();
